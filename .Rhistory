help.start()
help.start()
q()
library(jiebaR)
library(readxl)
library(stm)
library(stmCorrViz)
library(tm)
# 读取Excel文件
data <- read_excel("D:/Rwork/CNKI_电子商务_最终版数据.xlsx")
# 中文分词
eng <- worker()
seg_list <- lapply(data$摘要, segment, eng)
fencijg <- lapply(seg_list, paste, collapse = " ")
fenci <- unlist(fencijg)
# 将分词后的数据放入原列表
data$new_abstract <- fenci
# wordLength=c(3,Inf)表示移除短于3，长于inf的词语：中文调为2
processed <- textProcessor(data$new_abstract,metadata=data,wordLengths=c(2,Inf))
# 查看不同阈值删除doc,words,token情况
pdf("D:/Rwork/stm-plot-removed4.pdf")
plotRemoved(processed$documents, lower.thresh = seq(1, 200, by = 10))
dev.off()
# 建立完整文档-协变量数据
out <- prepDocuments(
documents = processed$documents, #包含索引词的计数及文档列表
vocab = processed$vocab, # 索引词的关联单词
meta = processed$meta, #包含文档协变量
lower.thresh = 20)
# 模型拟合
poliblogPrevFit <- stm(documents = out$documents,
vocab = out$vocab,
K = 10, #主题数
prevalence =~ s(发表时间),
# 主题偏好模型指定的协变量
content = ~来源,
# 词语偏好模型指定的协变量
max.em.its = 50, #最大迭代数
data = out$meta,init.type = "Spectral"
)
save.image("D:/Rwork/mystm1_poliblogPrevFit.RData")
View(poliblogPrevFit)
source("D:/Download/STM/new1.R")
source("D:/Download/STM/new1.R")
source("D:/Download/STM/new1.R")
library(jiebaR)
library(readxl)
library(stm)
library(stmCorrViz)
library(tm)
poliblogSelect <- selectModel(
out$documents, out$vocab, K = 10, prevalence = ~发表时间,
max.em.its = 50, data = out$meta,
runs = 20, #20 models
seed = 3239
)
pdf("stmVignette-009.pdf")
plotModels(poliblogSelect)
dev.off()
plotModels(poliblogSelect, pch = c(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20),
legend.position = 'bottomright')
pdf("stmVignette-009.pdf")
source("D:/Download/STM/new1.R")
save.image("D:/Rwork/mystm_finishdata.RData")
# estimateEffect
out$meta$stage <- as.factor(out$meta$发表时间)
prep <- estimateEffect(
# 对所有主题的影响
1:10 ~ s(发表时间), poliblogPrevFit, meta=out$meta,
uncertainty = 'Global' # OR "Local", "None"
# 考虑全局不确定性，选择None会加速计算时间，获得更窄的置信区间
)
library(jiebaR)
library(stm)
library(stmCorrViz)
library(tm)
# estimateEffect
out$meta$stage <- as.factor(out$meta$发表时间)
prep <- estimateEffect(
# 对所有主题的影响
1:10 ~ s(发表时间), poliblogPrevFit, meta=out$meta,
uncertainty = 'Global' # OR "Local", "None"
# 考虑全局不确定性，选择None会加速计算时间，获得更窄的置信区间
)
summary(prep, topics=1)
summary(prep, topics=2)
source("D:/Download/STM/new1.R")
# 主题时序变化图
# pdf("stm-plot-prevalence-trends.pdf")
plot(
prep, "days with numeric type", method = "continuous", topics = 7,
model = z, printlegend = FALSE, xaxt = "n", xlab = "发表时间"
)
setwd("D:/Download/R_Student_Analysis")
plot4 <- ggplot(comparison_long, aes(x = reorder(Identifier, Score), y = Score, fill = Type)) +
geom_bar(stat = "identity", position = position_dodge(width = 0.8), width=0.7) +
labs(
title = "部分学生实际分数与预测分数对比",
x = "学生标识",
y = "中考分数",
fill = "分数类型"
) +
theme_minimal(base_family = "sans") +
theme(axis.text.x = element_text(angle = 45, hjust = 1))
setwd("D:/Download/R_Student_Analysis")
install.packages("ggplot2")
# 显示图表
# 如果需要，使用 patchwork 组合图表
if (nrow(regression_data) > 0) {
print(plot1)
print(plot2)
print(plot3)
if (nrow(comparison_subset) > 0) print(plot4)
# 您也可以将它们排列在一个网格中
# combined_plot <- (plot1 | plot2) / (plot3 | plot4)
# print(combined_plot + plot_layout(guides = 'collect') + plot_annotation(title = '回归分析总览', theme = theme(plot.title = element_text(family="sans"))))
} else {
print("由于移除NA后没有数据用于回归，因此跳过绘图。")
}
source("D:/Download/R_Student_Analysis/student_regression_analysis.R")
source("D:/Download/R_Student_Analysis/student_regression_analysis.R", echo=TRUE)
source("D:/Download/R_Student_Analysis/student_regression_analysis.R", echo=TRUE)
